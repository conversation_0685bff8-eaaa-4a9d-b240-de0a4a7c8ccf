<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form

      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择类型"
          clearable
          class="!w-140px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MCP_SERVER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="启用" prop="enabled">
        <el-select
          v-model="queryParams.enabled"
          placeholder="请选择"
          clearable
          class="!w-120px"
        >
          <el-option
            v-for="dict in getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <div class="flex justify-between">
      <div class="action-buttons">
        <el-button
          type="primary"
          @click="openForm('create')"
          v-hasPermi="['ai:mcp-server:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="info"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['ai:mcp-server:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </div>

      <!-- 视图切换按钮 -->
      <div class="view-toggle-container">
        <span class="toggle-label">视图模式:</span>
        <el-radio-group v-model="viewMode" size="default">
          <el-radio-button label="table" class="view-mode-btn">
            <Icon icon="ep:grid" class="view-icon" />
            <span>表格</span>
          </el-radio-button>
          <el-radio-button label="card" class="view-mode-btn">
            <Icon icon="ep:menu" class="view-icon" />
            <span>卡片</span>
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap v-if="viewMode === 'table'">
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" border>
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="名称" align="center" prop="name" width="200" />
      <el-table-column label="类型" align="center" prop="type" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MCP_SERVER_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="命令" align="center" prop="command" width="100" />
      <el-table-column label="参数" align="center" prop="args" />
      <el-table-column label="环境变量" align="center" prop="env" />
      <el-table-column label="URL" align="center" prop="url" />
      <el-table-column label="启用标识" align="center" prop="enabled" width="90">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.enabled" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="140px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['ai:mcp-server:update']"
          >
            <Icon icon="ep:edit" />编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['ai:mcp-server:delete']"
          >
            <Icon icon="ep:delete" />删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 卡片视图 -->
  <ContentWrap v-else>
    <div v-loading="loading" class="card-container">
      <el-empty v-if="list.length === 0" description="暂无数据" />
      <el-row :gutter="16" v-else>
        <el-col v-for="item in list" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6" :xl="6" class="mb-4">
          <el-card shadow="hover" class="card-item">
            <template #header>
              <div class="card-header">
                <span class="card-title">{{ item.name }}</span>
                <dict-tag :type="DICT_TYPE.MCP_SERVER_TYPE" :value="item.type" class="ml-2" />
                <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="item.enabled" class="ml-2" />
              </div>
            </template>
            <div class="card-content">
              <el-tooltip
                v-if="item.descn"
                :content="item.descn"
                placement="top"
                :show-after="200"
                max-width="300"
              >
                <div class="card-desc">{{ item.descn }}</div>
              </el-tooltip>
              <div v-else class="card-desc empty-desc">暂无描述</div>
            </div>
            <div class="card-actions">
              <el-button
                size="small"
                type="primary"
                circle
                @click="openForm('update', item.id)"
                v-hasPermi="['ai:mcp-server:update']"
              >
                <Icon icon="ep:edit" />
              </el-button>
              <el-button
                size="small"
                type="danger"
                circle
                @click="handleDelete(item.id)"
                v-hasPermi="['ai:mcp-server:delete']"
              >
                <Icon icon="ep:delete" />
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <McpServerForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getStrDictOptions, getBoolDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { McpServerApi, McpServerVO } from '@/api/ai/mcpserver'
import McpServerForm from './McpServerForm.vue'

/** MCP服务器 列表 */
defineOptions({ name: 'McpServer' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<McpServerVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const viewMode = ref('table') // 视图模式: 'table' 或 'card'
const queryParams = reactive({
  pageNo: 1,
  pageSize: viewMode.value === 'card' ? 20 : 10,
  name: undefined,
  type: undefined,
  enabled: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await McpServerApi.getMcpServerPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 切换视图模式 */
watch(viewMode, (newValue) => {
  queryParams.pageSize = newValue === 'card' ? 20 : 10
  getList()
})

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await McpServerApi.deleteMcpServer(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await McpServerApi.exportMcpServer(queryParams)
    download.excel(data, 'MCP服务器.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.view-toggle-container {
  display: inline-flex;
  align-items: center;
  margin-left: auto;

  .toggle-label {
    margin-right: 8px;
    font-size: 14px;
  }

  :deep(.view-mode-btn) {
    display: inline-flex;

    .el-radio-button__inner {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      padding: 8px 15px;
    }

    .view-icon {
      margin-right: 6px;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      height: 14px;
      line-height: 1;
    }

    span {
      line-height: 1;
      font-size: 14px;
    }
  }
}

.-mb-15px {
  display: flex;
  flex-wrap: wrap;

  :deep(.el-form-item:last-child) {
    margin-left: auto;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
}

.card-container {
  .card-item {
    height: 100%;
    transition: all 0.3s;
    position: relative;
    padding-bottom: 42px;

    &:hover {
      transform: translateY(-5px);
    }

    :deep(.el-card__header) {
      padding: 6px 12px;
      min-height: auto;
    }

    :deep(.el-card__body) {
      padding: 12px;
    }

    .card-header {
      display: flex;
      align-items: center;

      .card-title {
        font-weight: bold;
        font-size: 15px;
        flex-grow: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .card-content {
      .card-desc {
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.4;
        word-break: break-word;
        color: var(--el-text-color-regular);
        font-size: 13px;
      }

      .empty-desc {
        color: var(--el-text-color-secondary);
        font-style: italic;
        font-size: 13px;
      }
    }

    .card-actions {
      position: absolute;
      bottom: 12px;
      right: 12px;
      display: flex;
      gap: 8px;
    }
  }
}
</style>
