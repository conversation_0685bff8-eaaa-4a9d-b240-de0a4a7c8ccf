<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称"/>
      </el-form-item>
      <el-form-item label="描述" prop="descn">
        <el-input v-model="formData.descn" type="textarea" placeholder="请输入描述"/>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-radio-group v-model="formData.type">
          <el-radio
            v-for="dict in getStrDictOptions(DICT_TYPE.MCP_SERVER_TYPE)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="命令" prop="command" v-if="formData.type === 'STDIO'">
        <el-input v-model="formData.command" placeholder="请输入命令"/>
      </el-form-item>
      <el-form-item label="参数" prop="args" v-if="formData.type === 'STDIO'">
        <el-input v-model="formData.args" type="textarea" :rows="4" placeholder="请输入参数"/>
      </el-form-item>
      <el-form-item label="环境变量" prop="env" v-if="formData.type === 'STDIO'">
        <el-input v-model="formData.env" type="textarea" :rows="3" placeholder="请输入环境变量"/>
      </el-form-item>
      <el-form-item label="URL" prop="url" v-if="formData.type !== 'STDIO'">
        <el-input v-model="formData.url" type="textarea" placeholder="请输入URL"/>
      </el-form-item>
      <el-form-item label="HTTP请求头" prop="headers" v-if="formData.type !== 'STDIO'">
        <el-input v-model="formData.headers" type="textarea" placeholder="请输入HTTP请求头"/>
      </el-form-item>
      <el-form-item label="启用" prop="enabled">
        <el-radio-group v-model="formData.enabled">
          <el-radio-button v-for="dict in getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)" :key="dict.value" :label="dict.value">
            {{ dict.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import {getStrDictOptions, getBoolDictOptions, DICT_TYPE} from '@/utils/dict'
import {McpServerApi, McpServerVO} from '@/api/ai/mcpserver'

/** MCP服务器 表单 */
defineOptions({name: 'McpServerForm'})

const {t} = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  descn: undefined,
  type: 'STDIO',
  command: undefined,
  args: undefined,
  env: undefined,
  url: undefined,
  headers: undefined,
  enabled: true
})
const formRules = reactive({})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await McpServerApi.getMcpServer(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as McpServerVO
    if (formType.value === 'create') {
      await McpServerApi.createMcpServer(data)
      message.success(t('common.createSuccess'))
    } else {
      await McpServerApi.updateMcpServer(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    descn: undefined,
    type: 'STDIO',
    command: undefined,
    args: undefined,
    env: undefined,
    url: undefined,
    headers: undefined,
    enabled: true
  }
  formRef.value?.resetFields()
}
</script>
