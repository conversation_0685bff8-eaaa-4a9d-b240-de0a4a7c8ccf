# 简历解析功能测试指南

## 功能概述

简历上传功能现在支持异步解析，包含以下状态：
- 0: 未解析
- 1: 解析中
- 2: 解析成功  
- 3: 解析失败

## 测试步骤

### 1. 上传简历测试

1. 访问人员管理页面 `/hr/person`
2. 点击"上传简历"按钮
3. 输入姓名并上传简历文件
4. 点击"确定"
5. 验证：
   - 显示成功提示："简历上传成功，已创建人员记录，系统正在解析简历内容，请稍后查看"
   - 人员列表中新记录的简历文件列显示"解析中"状态（橙色标签，带加载图标）

### 2. 解析状态显示测试

在简历文件列中应该能看到：
- **未解析**：灰色标签
- **解析中**：橙色标签，带旋转加载图标
- **解析成功**：绿色标签
- **解析失败**：红色标签

### 3. 操作菜单测试

点击"更多"下拉菜单，根据解析状态应显示不同选项：

#### 未解析或解析失败状态
- 下载
- 重新上传
- **触发解析**（新增）

#### 解析中状态
- 下载
- 重新上传
- **检查状态**（新增）

#### 解析成功状态
- 下载
- 重新上传

### 4. 手动触发解析测试

1. 找到状态为"未解析"或"解析失败"的记录
2. 点击"更多" → "触发解析"
3. 确认弹窗
4. 验证：
   - 显示成功提示："已触发简历解析，请稍后查看解析结果"
   - 状态更新为"解析中"

### 5. 检查状态测试

1. 找到状态为"解析中"的记录
2. 点击"更多" → "检查状态"
3. 验证：
   - 显示当前解析状态的消息
   - 列表自动刷新

### 6. 自动状态更新测试

1. 确保有"解析中"状态的记录
2. 等待30秒
3. 验证：
   - 页面自动刷新获取最新状态
   - 无需手动刷新页面

### 7. 重新上传简历测试

1. 点击"更多" → "重新上传"
2. 选择新的简历文件并上传
3. 验证：
   - 显示成功提示："简历重新上传成功，系统正在解析简历内容，请稍后查看"
   - 状态重置为"解析中"

## API接口测试

### 获取解析状态
```
GET /hr/person/resume-parse-status?personId={personId}
```

### 触发解析
```
POST /hr/person/trigger-resume-parse
Body: { "personId": 123 }
```

## 注意事项

1. 定时器每30秒检查一次解析状态
2. 只有当存在"解析中"状态的记录时才会自动刷新
3. 页面卸载时会自动清理定时器
4. 解析过程是异步的，可能需要较长时间
5. 解析状态会实时反映在界面上

## 错误处理

- 网络错误时会显示相应错误提示
- API调用失败时会有错误日志
- 用户取消操作时不会显示错误信息
