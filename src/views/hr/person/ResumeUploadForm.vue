<template>
  <Dialog
    v-model="dialogVisible"
    :title="reuploadPerson ? '重新上传简历' : '上传简历'"
    width="500px"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="80px">
      <el-form-item label="解析模型" prop="modelId">
        <el-select v-model="formData.modelId" placeholder="请选择模型" clearable>
          <el-option
            v-for="model in models"
            :key="model.id"
            :label="model.name"
            :value="model.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="简历文件" prop="resumeUrl">
        <el-upload
          ref="uploadRef"
          v-model:file-list="fileList"
          :action="uploadUrl"
          :auto-upload="false"
          :data="uploadData"
          :disabled="formLoading"
          :limit="1"
          :on-change="handleFileChange"
          :on-error="submitFormError"
          :on-exceed="handleExceed"
          :on-success="submitFormSuccess"
          :http-request="httpRequest"
          accept=".pdf,.doc,.docx"
          drag
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text"> 将简历文件拖到此处，或 <em>点击上传</em> </div>
          <template #tip>
            <div class="el-upload__tip" style="color: red">
              提示：仅允许导入 PDF、DOC、DOCX 格式的简历文件！
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { useUpload } from '@/components/UploadFile/src/useUpload'
import { PersonApi } from '@/api/hr/person'
import { UploadFilled } from '@element-plus/icons-vue'
import {ModelApi, ModelVO} from "@/api/ai/model/model";
import {AiModelTypeEnum} from "@/views/ai/utils/constants";

defineOptions({ name: 'ResumeUploadForm' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const fileList = ref([]) // 文件列表
const uploadData = ref({ path: '' })
const uploadRef = ref()
const formRef = ref()

// 表单数据
const formData = ref({
  modelId: undefined,
  resumeUrl: ''
})

const models = ref([] as ModelVO[]) // 聊天模型列表

// 重新上传模式的人员信息
const reuploadPerson = ref(null)

// 表单验证规则
const formRules = reactive({
  name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }]
})

const { uploadUrl, httpRequest } = useUpload('resume') // 指定上传目录为 resume

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  reuploadPerson.value = null
  resetForm()
  // 获得下拉数据
  models.value = await ModelApi.getModelSimpleList(AiModelTypeEnum.CHAT)
}

/** 打开重新上传弹窗 */
const openForReupload = async (person: any) => {
  dialogVisible.value = true
  reuploadPerson.value = person
  resetForm()
}

defineExpose({ open, openForReupload }) // 提供 open 方法，用于打开弹窗

/** 处理上传的文件发生变化 */
const handleFileChange = (file) => {
  uploadData.value.path = file.name
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return

  // 检查是否上传了文件
  if (fileList.value.length === 0) {
    message.error('请上传简历文件')
    return
  }

  // 先上传文件
  formLoading.value = true
  try {
    // 上传文件
    await uploadRef.value?.submit()
  } catch (error) {
    formLoading.value = false
    message.error('文件上传失败，请重试')
  }
}

/** 文件上传成功处理 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitFormSuccess = async (response: any) => {
  try {
    // 获取上传后的文件URL
    const fileUrl = response.data || response.url

    if (reuploadPerson.value) {
      // 重新上传模式：更新现有人员的简历URL，并设置解析状态为解析中
      const updateData = {
        ...reuploadPerson.value,
        resumeUrl: fileUrl,
        resumeParseStatus: 1 // 设置为解析中状态
      }
      await PersonApi.updatePerson(updateData)
      message.success('简历重新上传成功，系统正在解析简历内容，请稍后查看')
    } else {
      // 新建模式：创建人员记录，只填写姓名和简历URL，并设置解析状态为解析中
      const personData = {
        id: 0, // 新建时ID为0
        name: formData.value.name,
        resumeUrl: fileUrl,
        resumeParseStatus: 1, // 设置为解析中状态
        // 其他字段使用默认值或空值
        gender: '',
        age: 0,
        birthDate: '',
        idCard: '',
        phone: '',
        email: '',
        city: '',
        address: '',
        registeredAddress: '',
        ethnicity: '',
        nationality: '',
        politicalStatus: '',
        maritalStatus: '',
        school: '',
        degree: '',
        educationLevel: '',
        companyName: '',
        position: '',
        employmentStatus: '',
        workYear: '',
        workStartDate: '',
        availableDate: '',
        jobType: '',
        avatarUrl: ''
      }
      await PersonApi.createPerson(personData)
      message.success('简历上传成功，已创建人员记录，系统正在解析简历内容，请稍后查看')
    }

    // 清理
    dialogVisible.value = false
    formLoading.value = false
    uploadRef.value?.clearFiles()

    // 刷新列表
    emit('success')
  } catch (error) {
    formLoading.value = false
    message.error('创建人员记录失败，请重试')
  }
}

/** 上传错误提示 */
const submitFormError = (): void => {
  message.error('上传失败，请您重新上传！')
  formLoading.value = false
}

/** 重置表单 */
const resetForm = () => {
  // 重置表单数据（重新上传模式下保留姓名）
  if (!reuploadPerson.value) {
    formData.value = {
      name: '',
      resumeUrl: ''
    }
  } else {
    formData.value.resumeUrl = ''
  }
  // 重置上传状态和文件
  formLoading.value = false
  uploadRef.value?.clearFiles()
  fileList.value = []
  // 重置表单验证
  formRef.value?.resetFields()
}

/** 文件数超出提示 */
const handleExceed = (): void => {
  message.error('最多只能上传一个简历文件！')
}
</script>

<style scoped>
.el-upload__tip {
  margin-top: 8px;
}
</style>
