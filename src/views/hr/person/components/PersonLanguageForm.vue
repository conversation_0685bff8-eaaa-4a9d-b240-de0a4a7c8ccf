<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" width="100" />
       <el-table-column label="语言名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.languageName`" :rules="formRules.languageName" class="mb-0px!">
            <el-select v-model="row.languageName" placeholder="请选择语言名称">
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.LANGUAGE_NAME)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="熟练程度" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.proficiencyLevel`" :rules="formRules.proficiencyLevel" class="mb-0px!">
            <el-select v-model="row.proficiencyLevel" placeholder="请选择熟练程度">
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.PROFICIENCY_LEVEL)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加语言能力</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { PersonApi } from '@/api/hr/person'

const props = defineProps<{
  personId: number // 人员ID（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])
const formRules = reactive({
  personId: [{ required: true, message: '人员ID不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.personId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      formData.value = await PersonApi.getPersonLanguageListByPersonId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    personId: undefined,
    languageName: undefined,
    proficiencyLevel: undefined
  }
  row.personId = props.personId as any
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>