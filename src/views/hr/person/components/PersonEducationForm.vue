<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" width="100" />
      <el-table-column label="学校名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.schoolName`"
            :rules="formRules.schoolName"
            class="mb-0px!"
          >
            <el-input v-model="row.schoolName" placeholder="请输入学校名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.startDate`" :rules="formRules.startDate" class="mb-0px!">
            <el-date-picker
              v-model="row.startDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择开始时间"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.endDate`" :rules="formRules.endDate" class="mb-0px!">
            <el-date-picker
              v-model="row.endDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="选择结束时间"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="专业" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.major`" :rules="formRules.major" class="mb-0px!">
            <el-input v-model="row.major" placeholder="请输入专业" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="学历" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.educationLevel`"
            :rules="formRules.educationLevel"
            class="mb-0px!"
          >
            <el-select v-model="row.educationLevel" placeholder="请选择学历">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.EDUCATION_LEVEL)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="学位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.degree`" :rules="formRules.degree" class="mb-0px!">
            <el-select v-model="row.degree" placeholder="请选择学位">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.DEGREE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加教育经历</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { PersonApi } from '@/api/hr/person'

const props = defineProps<{
  personId: number // 人员ID（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])
const formRules = reactive({
  personId: [{ required: true, message: '人员ID不能为空', trigger: 'blur' }],
  schoolName: [{ required: true, message: '学校名称不能为空', trigger: 'blur' }],
  startDate: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
  endDate: [{ required: true, message: '结束时间不能为空', trigger: 'blur' }],
  major: [{ required: true, message: '专业不能为空', trigger: 'blur' }],
  educationLevel: [{ required: true, message: '学历不能为空', trigger: 'change' }],
  degree: [{ required: true, message: '学位不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.personId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return
    }
    try {
      formLoading.value = true
      formData.value = await PersonApi.getPersonEducationListByPersonId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    personId: undefined,
    schoolName: undefined,
    startDate: undefined,
    endDate: undefined,
    major: undefined,
    educationLevel: undefined,
    degree: undefined
  }
  row.personId = props.personId as any
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>
