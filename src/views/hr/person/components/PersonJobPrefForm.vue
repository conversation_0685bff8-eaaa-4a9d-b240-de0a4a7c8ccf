<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" width="100" />
       <el-table-column label="期望职位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.expectedPosition`" :rules="formRules.expectedPosition" class="mb-0px!">
            <el-input v-model="row.expectedPosition" placeholder="请输入期望职位" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="期望行业" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.expectedIndustry`" :rules="formRules.expectedIndustry" class="mb-0px!">
            <el-input v-model="row.expectedIndustry" placeholder="请输入期望行业" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="期望工作城市" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.expectedCity`" :rules="formRules.expectedCity" class="mb-0px!">
            <el-input v-model="row.expectedCity" placeholder="请输入期望工作城市" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="期望薪资" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.expectedSalary`" :rules="formRules.expectedSalary" class="mb-0px!">
            <el-input v-model="row.expectedSalary" placeholder="请输入期望薪资" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加求职期望</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { PersonApi } from '@/api/hr/person'

const props = defineProps<{
  personId: number // 人员ID（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])
const formRules = reactive({
  personId: [{ required: true, message: '人员ID不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.personId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      formData.value = await PersonApi.getPersonJobPrefListByPersonId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    personId: undefined,
    expectedPosition: undefined,
    expectedIndustry: undefined,
    expectedCity: undefined,
    expectedSalary: undefined
  }
  row.personId = props.personId as any
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>