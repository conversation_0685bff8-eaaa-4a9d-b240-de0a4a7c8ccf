import request from '@/config/axios'
import type { Dayjs } from 'dayjs'

/** 资格证书信息 */
export interface PersonCertificate {
  id: number // ID
  personId?: number // 人员ID
  certificateName: string // 证书名称
  certificateCode: string // 证书编号
  issuer: string // 发证机构
  issueDate: string | Dayjs // 颁发日期
  expiryDate: string | Dayjs // 有效期至
}

/** 教育经历信息 */
export interface PersonEducation {
  id: number // ID
  personId?: number // 人员ID
  schoolName?: string // 学校名称
  startDate?: string | Dayjs // 开始时间
  endDate?: string | Dayjs // 结束时间
  major?: string // 专业
  educationLevel?: string // 学历
  degree?: string // 学位
}

/** 求职期望信息 */
export interface PersonJobPref {
  id: number // ID
  personId?: number // 人员ID
  expectedPosition: string // 期望职位
  expectedIndustry: string // 期望行业
  expectedCity: string // 期望工作城市
  expectedSalary: string // 期望薪资
}

/** 语言能力信息 */
export interface PersonLanguage {
  id: number // ID
  personId?: number // 人员ID
  languageName: string // 语言名称
  proficiencyLevel: string // 熟练程度
}

/** 项目经历信息 */
export interface PersonProject {
  id: number // ID
  personId?: number // 人员ID
  projectName: string // 项目名称
  companyName: string // 公司名称
  startDate?: string | Dayjs // 开始时间
  endDate?: string | Dayjs // 结束时间
  technologies: string // 技术栈
  projectDesc: string // 项目描述
  projectRole: string // 项目职位
  responsibilities: string // 项目职责
  projectAchievement: string // 项目业绩
}

/** 技能特长信息 */
export interface PersonSkill {
  id: number // ID
  personId?: number // 人员ID
  skillId?: number // 技能ID
  proficiencyLevel: string // 熟练程度
  yearsOfExperience: number // 使用年限
}

/** 培训经历信息 */
export interface PersonTraining {
  id: number // ID
  personId?: number // 人员ID
  trainingName: string // 培训名称
  startDate?: string | Dayjs // 开始时间
  endDate?: string | Dayjs // 结束时间
  trainingOrg: string // 培训机构
  trainingDesc: string // 培训内容
  certificateName: string // 所获证书名称
}

/** 工作经历信息 */
export interface PersonWork {
  id: number // ID
  personId?: number // 人员ID
  companyName: string // 公司名称
  startDate?: string | Dayjs // 开始时间
  endDate?: string | Dayjs // 结束时间
  industry: string // 行业
  position: string // 职位
}

/** 人员基本信息信息 */
export interface Person {
  id: number // ID
  name: string // 姓名
  gender: string // 性别
  age: number // 年龄
  birthDate: string | Dayjs // 出生日期
  idCard: string // 身份证号
  phone: string // 手机号码
  email: string // 邮箱地址
  city: string // 城市
  address: string // 现居住地址
  registeredAddress: string // 户籍地址
  ethnicity: string // 民族
  nationality: string // 国籍
  politicalStatus: string // 政治面貌
  maritalStatus: string // 婚姻状况
  school: string // 毕业学校
  degree: string // 最高学位
  educationLevel: string // 最高学历
  companyName: string // 公司名称
  position: string // 职位
  employmentStatus: string // 在职状态
  workYear: string // 工作年限
  workStartDate: string | Dayjs // 参加工作时间
  availableDate: string // 可入职时间
  jobType: string // 求职类型
  avatarUrl: string // 头像URL
  resumeUrl: string // 简历文件URL
  resumeParseStatus?: number // 简历解析状态：0-未解析，1-解析中，2-解析成功，3-解析失败
  modelId?: number // 简历解析模型ID
  personCertificates?: PersonCertificate[]
  personEducations?: PersonEducation[]
  personJobPrefs?: PersonJobPref[]
  personLanguages?: PersonLanguage[]
  personProjects?: PersonProject[]
  personSkills?: PersonSkill[]
  personTrainings?: PersonTraining[]
  personWorks?: PersonWork[]
}

// 人员基本信息 API
export const PersonApi = {
  // 查询人员基本信息分页
  getPersonPage: async (params: any) => {
    return await request.get({ url: `/hr/person/page`, params })
  },

  // 查询人员基本信息详情
  getPerson: async (id: number) => {
    return await request.get({ url: `/hr/person/get?id=` + id })
  },

  // 新增人员基本信息
  createPerson: async (data: Person) => {
    return await request.post({ url: `/hr/person/create`, data })
  },

  // 修改人员基本信息
  updatePerson: async (data: Person) => {
    return await request.put({ url: `/hr/person/update`, data })
  },

  // 删除人员基本信息
  deletePerson: async (id: number) => {
    return await request.delete({ url: `/hr/person/delete?id=` + id })
  },

  /** 批量删除人员基本信息 */
  deletePersonList: async (ids: number[]) => {
    return await request.delete({ url: `/hr/person/delete-list?ids=${ids.join(',')}` })
  },

  // 导出人员基本信息 Excel
  exportPerson: async (params) => {
    return await request.download({ url: `/hr/person/export-excel`, params })
  },

  // 获取简历解析状态
  getResumeParseStatus: async (personId: number) => {
    return await request.get({ url: `/hr/person/resume-parse-status?personId=` + personId })
  },

  // 手动触发简历解析
  triggerResumeParse: async (personId: number) => {
    return await request.post({ url: `/hr/person/trigger-resume-parse`, data: { personId } })
  },

  // ==================== 子表（资格证书） ====================

  // 获得资格证书列表
  getPersonCertificateListByPersonId: async (personId) => {
    return await request.get({
      url: `/hr/person/person-certificate/list-by-person-id?personId=` + personId
    })
  },

  // ==================== 子表（教育经历） ====================

  // 获得教育经历列表
  getPersonEducationListByPersonId: async (personId) => {
    return await request.get({
      url: `/hr/person/person-education/list-by-person-id?personId=` + personId
    })
  },

  // ==================== 子表（求职期望） ====================

  // 获得求职期望列表
  getPersonJobPrefListByPersonId: async (personId) => {
    return await request.get({
      url: `/hr/person/person-job-pref/list-by-person-id?personId=` + personId
    })
  },

  // ==================== 子表（语言能力） ====================

  // 获得语言能力列表
  getPersonLanguageListByPersonId: async (personId) => {
    return await request.get({
      url: `/hr/person/person-language/list-by-person-id?personId=` + personId
    })
  },

  // ==================== 子表（项目经历） ====================

  // 获得项目经历列表
  getPersonProjectListByPersonId: async (personId) => {
    return await request.get({
      url: `/hr/person/person-project/list-by-person-id?personId=` + personId
    })
  },

  // ==================== 子表（技能特长） ====================

  // 获得技能特长列表
  getPersonSkillListByPersonId: async (personId) => {
    return await request.get({
      url: `/hr/person/person-skill/list-by-person-id?personId=` + personId
    })
  },

  // ==================== 子表（培训经历） ====================

  // 获得培训经历列表
  getPersonTrainingListByPersonId: async (personId) => {
    return await request.get({
      url: `/hr/person/person-training/list-by-person-id?personId=` + personId
    })
  },

  // ==================== 子表（工作经历） ====================

  // 获得工作经历列表
  getPersonWorkListByPersonId: async (personId) => {
    return await request.get({
      url: `/hr/person/person-work/list-by-person-id?personId=` + personId
    })
  }
}
