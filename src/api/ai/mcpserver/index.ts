import request from '@/config/axios'

// MCP服务器 VO
export interface McpServerVO {
  id: number // ID
  name: string // 名称
  descn: string // 描述
  type: string // 类型
  command: string // 命令
  args: string // 参数
  env: string // 环境变量
  url: string // URL
  headers: string // HTTP请求头
  enabled: boolean // 启用标识
}

// MCP服务器 API
export const McpServerApi = {
  // 查询MCP服务器分页
  getMcpServerPage: async (params: any) => {
    return await request.get({ url: `/ai/mcp-server/page`, params })
  },

  // 查询MCP服务器详情
  getMcpServer: async (id: number) => {
    return await request.get({ url: `/ai/mcp-server/get?id=` + id })
  },

  // 新增MCP服务器
  createMcpServer: async (data: McpServerVO) => {
    return await request.post({ url: `/ai/mcp-server/create`, data })
  },

  // 修改MCP服务器
  updateMcpServer: async (data: McpServerVO) => {
    return await request.put({ url: `/ai/mcp-server/update`, data })
  },

  // 删除MCP服务器
  deleteMcpServer: async (id: number) => {
    return await request.delete({ url: `/ai/mcp-server/delete?id=` + id })
  },

  // 导出MCP服务器 Excel
  exportMcpServer: async (params) => {
    return await request.download({ url: `/ai/mcp-server/export-excel`, params })
  }
}
