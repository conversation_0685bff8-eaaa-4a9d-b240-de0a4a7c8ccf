import request from '@/config/axios'

// 流程评价 VO
export interface ProcessCommentVO {
  id: number // ID
  processId: number // 流程ID
  rating: number // 评价等级
  content: string // 评价内容
  commenter: string // 评价人姓名
}

// 流程评价 API
export const ProcessCommentApi = {
  // 查询流程评价分页
  getProcessCommentPage: async (params: any) => {
    return await request.get({ url: `/rpa/process-comment/page`, params })
  },

  // 查询流程评价详情
  getProcessComment: async (id: number) => {
    return await request.get({ url: `/rpa/process-comment/get?id=` + id })
  },

  // 新增流程评价
  createProcessComment: async (data: ProcessCommentVO) => {
    return await request.post({ url: `/rpa/process-comment/create`, data })
  },

  // 修改流程评价
  updateProcessComment: async (data: ProcessCommentVO) => {
    return await request.put({ url: `/rpa/process-comment/update`, data })
  },

  // 删除流程评价
  deleteProcessComment: async (id: number) => {
    return await request.delete({ url: `/rpa/process-comment/delete?id=` + id })
  },

  // 导出流程评价 Excel
  exportProcessComment: async (params) => {
    return await request.download({ url: `/rpa/process-comment/export-excel`, params })
  }
}