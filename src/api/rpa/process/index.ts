import request from '@/config/axios'

// 流程 VO
export interface ProcessVO {
  id: number // ID
  commanderId: number // 控制中心ID
  categoryId: number // 分类ID
  code: string // 流程编码
  subCode: string // 流程子编码
  name: string // 流程名称
  descn: string // 流程描述
  released: string // 发布标识
  releaseTime: Date // 发布时间
  enabled: string // 启用标识
  version: string // 流程版本
  fileUrl: string // 文件地址
  deptId: number // 创建部门ID
  likeCount: number // 点赞数
  bookmarkCount: number // 收藏数
  commentCount: number // 评价数
  taskCount: number // 任务数
  runCount: number // 运行次数
  rating: number // 评分
}

// 流程 API
export const ProcessApi = {
  // 查询流程分页
  getProcessPage: async (params: any) => {
    return await request.get({url: `/rpa/process/page`, params})
  },

  // 查询流程详情
  getProcess: async (id: number) => {
    return await request.get({url: `/rpa/process/get?id=` + id})
  },

  // 新增流程
  createProcess: async (data: ProcessVO) => {
    return await request.post({url: `/rpa/process/create`, data})
  },

  // 修改流程
  updateProcess: async (data: ProcessVO) => {
    return await request.put({url: `/rpa/process/update`, data})
  },

  // 删除流程
  deleteProcess: async (id: number) => {
    return await request.delete({url: `/rpa/process/delete?id=` + id})
  },

  // 导出流程 Excel
  exportProcess: async (params) => {
    return await request.download({url: `/rpa/process/export-excel`, params})
  },

  // 获得已启用的流程列表
  getEnabledProcesses: async () => {
    return await request.get({url: `/rpa/process/list-all-enabled`})
  },

  // 获得收藏的流程分页
  getBookmarkProcessPage: async (params: any) => {
    return await request.get({url: `/rpa/process/bookmark-page`, params})
  }

}
