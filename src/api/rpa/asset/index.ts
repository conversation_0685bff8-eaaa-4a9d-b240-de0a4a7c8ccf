import request from '@/config/axios'

export interface AssetVO {
  id: number
  name: string
  scope: string
  type: string
  parentId: number
  templateId: number
  remark?: string
  value: string
  enabled: string
  deptId: number
  forceSync: string
}

// 数字资产 API
export const AssetApi = {
  // 查询数字资产分页
  getAssetPage: async (params: any) => {
    return await request.get({url: `/rpa/asset/page`, params})
  },

  // 查询数字资产详情
  getAsset: async (id: number) => {
    return await request.get({url: `/rpa/asset/get?id=` + id})
  },

  // 新增数字资产
  createAsset: async (data: AssetVO) => {
    return await request.post({url: `/rpa/asset/create`, data})
  },

  // 修改数字资产
  updateAsset: async (data: AssetVO) => {
    return await request.put({url: `/rpa/asset/update`, data})
  },

  // 删除数字资产
  deleteAsset: async (id: number) => {
    return await request.delete({url: `/rpa/asset/delete?id=` + id})
  },

  // 导出数字资产 Excel
  exportAsset: async (params) => {
    return await request.download({url: `/rpa/asset/export-excel`, params})
  },

  //查询模板资产
  getTemplateAssets: async () => {
    return await request.get({url: `/rpa/asset/templates`})
  }


}



