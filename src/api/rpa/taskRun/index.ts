import request from '@/config/axios'

// 任务运行记录 VO
export interface TaskRunVO {
  id: number // ID
  code: string // 记录编码
  taskId: number // 任务ID
  taskName: string // 任务名称
  taskCode: string // 任务编码
  priority: number // 优先级
  triggerMode: string // 触发方式
  scheduledTime: Date // 计划开始时间
  startTime: Date // 运行开始时间
  endTime: Date // 运行结束时间
  duration: number // 运行时长
  status: string // 运行状态
  botId: number // 机器人ID
  botCode: string // 机器人编码
  deviceName: string // 设备名称
  ip: string // IP地址
  remark: string // 备注
}

// 任务运行记录 API
export const TaskRunApi = {
  // 查询任务运行记录分页
  getTaskRunPage: async (params: any) => {
    return await request.get({ url: `/rpa/task-run/page`, params })
  },

  // 查询任务运行记录详情
  getTaskRun: async (id: number) => {
    return await request.get({ url: `/rpa/task-run/get?id=` + id })
  },

  // 新增任务运行记录
  createTaskRun: async (data: TaskRunVO) => {
    return await request.post({ url: `/rpa/task-run/create`, data })
  },

  // 修改任务运行记录
  updateTaskRun: async (data: TaskRunVO) => {
    return await request.put({ url: `/rpa/task-run/update`, data })
  },

  // 删除任务运行记录
  deleteTaskRun: async (id: number) => {
    return await request.delete({ url: `/rpa/task-run/delete?id=` + id })
  },

  // 导出任务运行记录 Excel
  exportTaskRun: async (params) => {
    return await request.download({ url: `/rpa/task-run/export-excel`, params })
  }
}