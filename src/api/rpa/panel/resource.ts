import request from '@/config/axios'

// 控制中心 VO
export interface ResourceMetric {
  commanderCount: number // 控制中心数量
  botCount: number // 机器人数量
}

// 统计资源指标卡
export const getResourceMetric = async () => {
  return await request.get({url: `/rpa/resource-panel/count-resource-metric`})
}

// 按机器人分类统计机器人数量
export const countBotByGroup = async () => {
  return await request.get({url: `/rpa/resource-panel/count-bot-by-category`})
}

// 机器人表格
export const findBotRun = async (params: any) => {
  return await request.get({url: `/rpa/resource-panel/bot-run`, params})
}

// 按时间统计机器人组使用率
export const botUsageByGroup = async (dateRange: string) => {
  return await request.get({url: `/rpa/resource-panel/bot-usage-by-group?dateRange=` + dateRange})
}

// 按机器人状态统计机器人数量
export const countBotByStatus = async () => {
  return await request.get({url: `/rpa/resource-panel/count-bot-by-status`})
}
