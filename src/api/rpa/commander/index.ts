import request from '@/config/axios'

// 控制中心 VO
export interface CommanderVO {
  id: number // ID
  name: string // 名称
  remark: string // 备注
  apiEndpoint: string // API端点
  status: string // 状态
  enabled: string // 启用标识
  manufacturer: string // 厂商(数据字典维护)
  apiKey: string // API账户
  apiSecret: string // API密钥
}

// 控制中心 API
export const CommanderApi = {
  // 查询控制中心分页
  getCommanderPage: async (params: any) => {
    return await request.get({url: `/rpa/commander/page`, params})
  },

  // 查询控制中心详情
  getCommander: async (id: number) => {
    return await request.get({url: `/rpa/commander/get?id=` + id})
  },

  // 新增控制中心
  createCommander: async (data: CommanderVO) => {
    return await request.post({url: `/rpa/commander/create`, data})
  },

  // 修改控制中心
  updateCommander: async (data: CommanderVO) => {
    return await request.put({url: `/rpa/commander/update`, data})
  },

  // 删除控制中心
  deleteCommander: async (id: number) => {
    return await request.delete({url: `/rpa/commander/delete?id=` + id})
  },

  // 导出控制中心 Excel
  exportCommander: async (params) => {
    return await request.download({url: `/rpa/commander/export-excel`, params})
  },

  // 获得控制中心精简列表
  getSimpleCommanders: async () => {
    return await request.get({url: `/rpa/commander/list-all-simple`})
  },

  // 获得已启用的控制中心列表
  getEnabledCommanders: async () => {
    return await request.get({url: `/rpa/commander/list-all-enabled`})
  }

}
