import request from '@/config/axios'

// 流程分类 VO
export interface ProcessCategoryVO {
  id: number // ID
  name: string // 分类名称
  descn: string // 分类描述
  enabled: string // 启用标识
}

// 流程分类 API
export const ProcessCategoryApi = {
  // 查询流程分类分页
  getProcessCategoryPage: async (params: any) => {
    return await request.get({url: `/rpa/process-category/page`, params})
  },

  // 查询流程分类详情
  getProcessCategory: async (id: number) => {
    return await request.get({url: `/rpa/process-category/get?id=` + id})
  },

  // 新增流程分类
  createProcessCategory: async (data: ProcessCategoryVO) => {
    return await request.post({url: `/rpa/process-category/create`, data})
  },

  // 修改流程分类
  updateProcessCategory: async (data: ProcessCategoryVO) => {
    return await request.put({url: `/rpa/process-category/update`, data})
  },

  // 删除流程分类
  deleteProcessCategory: async (id: number) => {
    return await request.delete({url: `/rpa/process-category/delete?id=` + id})
  },

  // 导出流程分类 Excel
  exportProcessCategory: async (params) => {
    return await request.download({url: `/rpa/process-category/export-excel`, params})
  },
  // 查询流程分类详情
  getSimpleProcessCategories: async () => {
    return await request.get({url: `/rpa/process-category/list-all-simple`})
  }
}
