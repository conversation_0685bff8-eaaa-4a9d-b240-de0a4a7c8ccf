import request from '@/config/axios'

// 流程参数 VO
export interface ProcessParamVO {
  id: number // ID
  processId: number // 流程ID
  name: string // 参数名称
  type: string // 数据类型
  value: string // 参数值
}

// 流程参数 API
export const ProcessParamApi = {
  // 查询流程参数分页
  getProcessParamPage: async (params: any) => {
    return await request.get({ url: `/rpa/process-param/page`, params })
  },

  // 查询流程参数详情
  getProcessParam: async (id: number) => {
    return await request.get({ url: `/rpa/process-param/get?id=` + id })
  },

  // 新增流程参数
  createProcessParam: async (data: ProcessParamVO) => {
    return await request.post({ url: `/rpa/process-param/create`, data })
  },

  // 修改流程参数
  updateProcessParam: async (data: ProcessParamVO) => {
    return await request.put({ url: `/rpa/process-param/update`, data })
  },

  // 删除流程参数
  deleteProcessParam: async (id: number) => {
    return await request.delete({ url: `/rpa/process-param/delete?id=` + id })
  },

  // 导出流程参数 Excel
  exportProcessParam: async (params) => {
    return await request.download({ url: `/rpa/process-param/export-excel`, params })
  }
}