import request from '@/config/axios'

// 任务 VO
export interface TaskVO {
  id: number // ID
  processId: number // 流程ID
  code: string // 任务编码
  name: string // 任务名称
  triggerMode: string // 触发方式
  priority: number // 优先级
  startTime: Date // 起始时间
  endTime: Date // 终止时间
  lastRunTime: Date // 上次运行时间
  nextRunTime: Date // 下次运行时间
  cronExpr: string // CRON表达式
  cronSummary: string // CRON含义
  distributionType: string // 机器人分配方式
  botGroupIds: string // 机器人分组ID数组
  runCount: number // 运行次数
  status: string // 任务状态
  enabled: string // 启用标识
}

// 任务 API
export const TaskApi = {
  // 查询任务分页
  getTaskPage: async (params: any) => {
    return await request.get({url: `/rpa/task/page`, params})
  },

  // 查询任务详情
  getTask: async (id: number) => {
    return await request.get({url: `/rpa/task/get?id=` + id})
  },

  // 新增任务
  createTask: async (data: TaskVO) => {
    return await request.post({url: `/rpa/task/create`, data})
  },

  // 修改任务
  updateTask: async (data: TaskVO) => {
    return await request.put({url: `/rpa/task/update`, data})
  },

  // 删除任务
  deleteTask: async (id: number) => {
    return await request.delete({url: `/rpa/task/delete?id=` + id})
  },

  // 导出任务 Excel
  exportTask: async (params) => {
    return await request.download({url: `/rpa/task/export-excel`, params})
  },

  // 运行任务
  runTask: async (id: number) => {
    return await request.put({url: `/rpa/task/run?id=` + id})
  }
}
