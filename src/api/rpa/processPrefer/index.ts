import request from '@/config/axios'

// 流程偏好 VO
export interface ProcessPreferVO {
  id: number // ID
  processId: number // 流程ID
  action: string // 操作类型
}

// 流程偏好 API
export const ProcessPreferApi = {
  // 查询流程偏好分页
  getProcessPreferPage: async (params: any) => {
    return await request.get({ url: `/rpa/process-prefer/page`, params })
  },

  // 查询流程偏好详情
  getProcessPrefer: async (id: number) => {
    return await request.get({ url: `/rpa/process-prefer/get?id=` + id })
  },

  // 新增流程偏好
  createProcessPrefer: async (data: ProcessPreferVO) => {
    return await request.post({ url: `/rpa/process-prefer/create`, data })
  },

  // 修改流程偏好
  updateProcessPrefer: async (data: ProcessPreferVO) => {
    return await request.put({ url: `/rpa/process-prefer/update`, data })
  },

  // 删除流程偏好
  deleteProcessPrefer: async (id: number) => {
    return await request.delete({ url: `/rpa/process-prefer/delete?id=` + id })
  },

  // 导出流程偏好 Excel
  exportProcessPrefer: async (params) => {
    return await request.download({ url: `/rpa/process-prefer/export-excel`, params })
  }
}