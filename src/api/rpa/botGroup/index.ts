import request from '@/config/axios'

// 机器人分组 VO
export interface BotGroupVO {
  id: number // ID
  name: string // 分组名称
  descn: string // 分组描述
}

// 机器人分组 API
export const BotGroupApi = {
  // 查询机器人分组分页
  getBotGroupPage: async (params: any) => {
    return await request.get({url: `/rpa/bot-group/page`, params})
  },

  // 查询机器人分组详情
  getBotGroup: async (id: number) => {
    return await request.get({url: `/rpa/bot-group/get?id=` + id})
  },

  // 新增机器人分组
  createBotGroup: async (data: BotGroupVO) => {
    return await request.post({url: `/rpa/bot-group/create`, data})
  },

  // 修改机器人分组
  updateBotGroup: async (data: BotGroupVO) => {
    return await request.put({url: `/rpa/bot-group/update`, data})
  },

  // 删除机器人分组
  deleteBotGroup: async (id: number) => {
    return await request.delete({url: `/rpa/bot-group/delete?id=` + id})
  },

  // 导出机器人分组 Excel
  exportBotGroup: async (params) => {
    return await request.download({url: `/rpa/bot-group/export-excel`, params})
  },

  // 获得机器人分组精简列表
  getSimpleBotGroups: async () => {
    return await request.get({url: `/rpa/bot-group/list-all-simple`})
  }

}
