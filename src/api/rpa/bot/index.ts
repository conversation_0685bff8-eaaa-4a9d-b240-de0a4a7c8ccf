import request from '@/config/axios'

// 机器人 VO
export interface BotVO {
  id: number // ID
  groupId: number // 机器人分组ID
  commanderId: number // 控制中心ID
  code: string // 编码
  name: string // 名称
  deviceName: string // 设备名称
  ip: string // IP地址
  version: string // 版本
  status: string // 机器人状态(在线:ONLINE|离线:OFFLINE|UNKNOWN:未知)
  lastConnectTime: Date // 最后连接时间
  taskQueueLength: number // 任务队列长度
}

// 机器人 API
export const BotApi = {
  // 查询机器人分页
  getBotPage: async (params: any) => {
    return await request.get({ url: `/rpa/bot/page`, params })
  },

  // 查询机器人详情
  getBot: async (id: number) => {
    return await request.get({ url: `/rpa/bot/get?id=` + id })
  },

  // 新增机器人
  createBot: async (data: BotVO) => {
    return await request.post({ url: `/rpa/bot/create`, data })
  },

  // 修改机器人
  updateBot: async (data: BotVO) => {
    return await request.put({ url: `/rpa/bot/update`, data })
  },

  // 删除机器人
  deleteBot: async (id: number) => {
    return await request.delete({ url: `/rpa/bot/delete?id=` + id })
  },

  // 导出机器人 Excel
  exportBot: async (params) => {
    return await request.download({ url: `/rpa/bot/export-excel`, params })
  }
}
