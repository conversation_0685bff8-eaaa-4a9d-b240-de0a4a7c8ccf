import request from '@/config/axios'

// 任务参数 VO
export interface TaskParamVO {
  id: number // ID
  taskId: number // 任务ID
  name: string // 任务参数名
  type: string // 数据类型
  value: string // 任务参数值
}

// 任务参数 API
export const TaskParamApi = {
  // 查询任务参数分页
  getTaskParamPage: async (params: any) => {
    return await request.get({ url: `/rpa/task-param/page`, params })
  },

  // 查询任务参数详情
  getTaskParam: async (id: number) => {
    return await request.get({ url: `/rpa/task-param/get?id=` + id })
  },

  // 新增任务参数
  createTaskParam: async (data: TaskParamVO) => {
    return await request.post({ url: `/rpa/task-param/create`, data })
  },

  // 修改任务参数
  updateTaskParam: async (data: TaskParamVO) => {
    return await request.put({ url: `/rpa/task-param/update`, data })
  },

  // 删除任务参数
  deleteTaskParam: async (id: number) => {
    return await request.delete({ url: `/rpa/task-param/delete?id=` + id })
  },

  // 导出任务参数 Excel
  exportTaskParam: async (params) => {
    return await request.download({ url: `/rpa/task-param/export-excel`, params })
  }
}