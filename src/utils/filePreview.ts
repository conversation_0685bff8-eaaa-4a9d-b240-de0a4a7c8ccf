import CryptoJS from 'crypto-js'

/**
 * 文件预览工具类
 * 基于kkFileView实现文件在线预览
 */

// kkFileView服务地址，可以通过环境变量配置
const KK_FILE_VIEW_URL = import.meta.env.VITE_KK_FILE_VIEW_URL || 'http://127.0.0.1:8012'

/**
 * 使用Base64编码URL（兼容crypto-js）
 * @param str 要编码的字符串
 * @returns Base64编码后的字符串
 */
function base64Encode(str: string): string {
  // 使用crypto-js的Base64编码
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(str))
}

/**
 * 使用kkFileView预览文件
 * @param fileUrl 文件的访问地址
 * @param fileName 文件名（可选，用于显示）
 * @returns 预览地址
 */
export function previewFileWithKkFileView(fileUrl: string, fileName?: string): string {
  if (!fileUrl) {
    throw new Error('文件URL不能为空')
  }

  // 对文件URL进行Base64编码
  const encodedUrl = base64Encode(fileUrl)
  
  // 构建kkFileView预览地址
  let previewUrl = `${KK_FILE_VIEW_URL}/onlinePreview?url=${encodeURIComponent(encodedUrl)}`
  
  // 如果提供了文件名，添加到预览地址中
  if (fileName) {
    previewUrl += `&fileName=${encodeURIComponent(fileName)}`
  }
  
  return previewUrl
}

/**
 * 在新窗口中预览文件
 * @param fileUrl 文件的访问地址
 * @param fileName 文件名（可选）
 */
export function openFilePreview(fileUrl: string, fileName?: string): void {
  try {
    const previewUrl = previewFileWithKkFileView(fileUrl, fileName)
    window.open(previewUrl, '_blank')
  } catch (error) {
    console.error('文件预览失败:', error)
    // 如果预览失败，降级为直接打开文件
    window.open(fileUrl, '_blank')
  }
}

/**
 * 检查文件是否支持预览
 * @param fileName 文件名或文件URL
 * @returns 是否支持预览
 */
export function isSupportedFileType(fileName: string): boolean {
  if (!fileName) return false
  
  const supportedExtensions = [
    // 文档类
    'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
    // 文本类
    'txt', 'md', 'xml', 'json', 'csv',
    // 图片类
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg',
    // 其他
    'rtf', 'odt', 'ods', 'odp'
  ]
  
  const extension = fileName.toLowerCase().split('.').pop()
  return extension ? supportedExtensions.includes(extension) : false
}

/**
 * 获取文件类型图标
 * @param fileName 文件名
 * @returns 图标类名
 */
export function getFileTypeIcon(fileName: string): string {
  if (!fileName) return 'ep:document'
  
  const extension = fileName.toLowerCase().split('.').pop()
  
  const iconMap: Record<string, string> = {
    pdf: 'ep:document',
    doc: 'ep:document',
    docx: 'ep:document',
    xls: 'ep:document',
    xlsx: 'ep:document',
    ppt: 'ep:document',
    pptx: 'ep:document',
    txt: 'ep:document-copy',
    md: 'ep:document-copy',
    jpg: 'ep:picture',
    jpeg: 'ep:picture',
    png: 'ep:picture',
    gif: 'ep:picture',
    bmp: 'ep:picture',
    webp: 'ep:picture',
    svg: 'ep:picture'
  }
  
  return iconMap[extension || ''] || 'ep:document'
}

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 * @returns 格式化后的文件大小
 */
export function formatFileSize(size: number): string {
  if (!size || size === 0) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  let fileSize = size
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }
  
  return `${fileSize.toFixed(2)} ${units[index]}`
}

export default {
  previewFileWithKkFileView,
  openFilePreview,
  isSupportedFileType,
  getFileTypeIcon,
  formatFileSize
}
